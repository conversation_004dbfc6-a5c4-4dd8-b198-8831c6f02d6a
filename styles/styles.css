/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #000;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Preloader Styles */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f0f9ff 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.preloader.fade-out {
    opacity: 0;
    visibility: hidden;
}

.preloader-content {
    text-align: center;
    position: relative;
}

.preloader .logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
    animation: logoFloat 2s ease-in-out infinite;
}

.preloader .logo-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #cda565, #b8935a);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    margin-right: 1rem;
    box-shadow: 0 8px 25px rgba(205, 165, 101, 0.3);
    position: relative;
    overflow: hidden;
}

.preloader .logo-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

.preloader .logo-text {
    font-size: 2rem;
    font-weight: 700;
    color: #000;
    background: linear-gradient(135deg, #000, #cda565);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-animation {
    margin-bottom: 2rem;
}

.loading-dots {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.loading-dots .dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: linear-gradient(135deg, #cda565, #b8935a);
    animation: dotPulse 1.5s ease-in-out infinite;
    box-shadow: 0 4px 12px rgba(205, 165, 101, 0.4);
}

.loading-dots .dot-1 { animation-delay: 0s; }
.loading-dots .dot-2 { animation-delay: 0.2s; }
.loading-dots .dot-3 { animation-delay: 0.4s; }
.loading-dots .dot-4 { animation-delay: 0.6s; }
.loading-dots .dot-5 { animation-delay: 0.8s; }

.loading-text {
    font-size: 1.1rem;
    font-weight: 500;
    color: #6b7280;
    letter-spacing: 0.5px;
    animation: textFade 2s ease-in-out infinite;
}

.progress-bar {
    width: 200px;
    height: 4px;
    background: rgba(205, 165, 101, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #cda565, #b8935a, #cda565);
    background-size: 200% 100%;
    border-radius: 2px;
    animation: progressFill 3s ease-in-out infinite, gradientShift 2s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(205, 165, 101, 0.5);
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
    padding: 0 2rem;
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    max-width: 1400px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo-icon {
    width: 32px;
    height: 32px;
    background: #cda565;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #000;
    font-weight: 500;
    font-size: 0.95rem;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #000;
}

.nav-buttons {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.btn-login {
    background: none;
    border: none;
    color: #000;
    font-weight: 500;
    cursor: pointer;
    padding: 0.5rem 1rem;
    transition: color 0.3s ease;
}

.btn-login:hover {
    color: #000;
}

.btn-touch {
    background: #cda565;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-touch:hover {
    background: #b8935a;
    transform: translateY(-1px);
}

.btn-touch i {
    font-size: 0.8rem;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 30px;
    height: 30px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 1001;
}

.mobile-menu-toggle span {
    display: block;
    height: 3px;
    width: 100%;
    background: #cda565;
    border-radius: 3px;
    transition: all 0.3s ease;
    transform-origin: center;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(7px, 7px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
}

/* Mobile Menu */
.mobile-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-100%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mobile-menu-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    text-align: center;
}

.mobile-nav-link {
    font-size: 1.5rem;
    font-weight: 600;
    color: #000;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 1rem 2rem;
    border-radius: 12px;
}

.mobile-nav-link:hover {
    color: #cda565;
    background: rgba(205, 165, 101, 0.1);
    transform: translateY(-2px);
}

.mobile-btn-touch {
    background: linear-gradient(135deg, #cda565, #b8935a);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.mobile-btn-touch:hover {
    background: linear-gradient(135deg, #b8935a, #a3824f);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(205, 165, 101, 0.3);
}

.mobile-btn-touch i {
    font-size: 1rem;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 120px 2rem 2rem;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)),
                url('https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80') center/cover;
    z-index: -10;
}

.hero-container {
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    min-height: 60vh;
}

.hero-content {
    flex: 1;
    max-width: 650px;
    position: relative;
    z-index: 5;
    padding-right: 2rem;
}

.hero-text {
    color: white;
    position: relative;
    z-index: 10;
}

.hero-slogan {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1rem;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 2.5rem;
    color: white;
}

.hero-buttons {
    display: flex !important;
    gap: 1rem;
    flex-wrap: wrap;
    position: relative;
    z-index: 100;
    margin-top: 1rem;
}

.hero-buttons button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 30px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    position: relative;
    z-index: 101;
    min-height: 50px;
    min-width: 150px;
}

.hero-buttons button span {
    color: inherit;
    font-weight: inherit;
}

.hero-buttons button i {
    color: inherit;
    font-size: 1rem;
    margin-right: 0.5rem;
}

#getInTouchBtn {
    background: #cda565 !important;
    color: white !important;
    border: none !important;
}

#getInTouchBtn:hover {
    background: #b8935a !important;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(205, 165, 101, 0.3);
}

#ourServicesBtn {
    background: transparent !important;
    color: white !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
}

#ourServicesBtn:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    transform: translateY(-2px);
}

/* Hero Card */
.hero-card {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 300px;
    backdrop-filter: blur(10px);
}

.card-image {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.card-image img {
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.card-dots {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: white;
    transform: scale(1.2);
}

.card-content h3 {
    color: #000;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    text-align: center;
}

/* Glass Effect */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Hero Stats */
.hero-stats {
    display: flex;
    justify-content: center;
    align-items: stretch;
    gap: 2rem;
    padding: 3rem 0 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    flex-wrap: wrap;
    position: relative;
    z-index: 10;
}

.stat-item {
    padding: 2rem 1.5rem;
    border-radius: 16px;
    text-align: center;
    width: 200px;
    height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 4px 16px rgba(16, 185, 129, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: white;
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
    z-index: -1;
}

.stat-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 50px rgba(0, 0, 0, 0.4),
        0 8px 25px rgba(16, 185, 129, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.stat-icon {
    font-size: 2.8rem;
    margin-bottom: 1.2rem;
    color: #cda565;
    text-shadow: 0 2px 10px rgba(205, 165, 101, 0.3);
}

.stat-number {
    font-size: 2.8rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    line-height: 1;
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.2;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
    text-align: center;
}

/* General Buttons */
.btn-general {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-general.btn-primary {
    background: #cda565;
    color: white;
}

.btn-general.btn-primary:hover {
    background: #b8935a;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(205, 165, 101, 0.3);
}

.btn-general.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-general.btn-secondary:hover {
    background: white;
    color: #cda565;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.3);
}

/* Trust Section */
.trust-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.trust-logos {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
}

.logo-item {
    display: flex;
    justify-content: center;
    align-items: center;
}

.logo-item img {
    height: 80px;
    max-width: 200px;
    object-fit: contain;
    opacity: 0.9;
    transition: all 0.3s ease;
    border-radius: 8px;
}

.logo-item img:hover {
    opacity: 1;
    transform: scale(1.05);
}

.trust-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 3rem;
}

.trust-description p {
    font-size: 1.2rem;
    color: #000;
    line-height: 1.8;
}

/* Form Toggle */
.form-toggle {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.toggle-btn {
    padding: 1rem 2rem;
    background: #ece2d8;
    border: 2px solid #cda565;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(205, 165, 101, 0.1);
}
.qouteToggleT {
    font-size: 14px;
    margin: 0;
    font-weight: 600;
    color: inherit;
}
.toggle-btn.active {
    background: linear-gradient(135deg, #cda565, #b8935a);
    color: white;
    border-color: #cda565;
    box-shadow: 0 4px 12px rgba(205, 165, 101, 0.3);
    transform: translateY(-1px);
}

.toggle-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(205, 165, 101, 0.2);
    border-color: #b8935a;
}

.toggle-btn.active:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(205, 165, 101, 0.4);
}

.toggle-btn i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

/* Forms */
.forms-container {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
}

.form {
    background: #ece2d8;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.8);
    padding: 2.5rem;
    border-radius: 24px;
    position: absolute;
    width: 100%;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.form.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    position: relative;
}

.form h3 {
    margin-bottom: 2rem;
    color: #000;
    text-align: center;
    font-size: 1.75rem;
    font-weight: 700;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #000;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    font-size: 1rem;
    color: #000;
    transition: all 0.3s ease;
    box-sizing: border-box;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #cda565;
    box-shadow: 0 0 0 3px rgba(205, 165, 101, 0.1);
    background: rgba(255, 255, 255, 0.9);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #9ca3af;
}

/* Select Wrapper */
.select-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
}

.select-wrapper::after {
    content: '';
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1rem;
    height: 1rem;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23cda565' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-size: contain;
    pointer-events: none;
    z-index: 1;
}

/* Custom Select Styling */
.form-group select {
    background: none;
    padding-right: 3rem;
    cursor: pointer;
}

/* Completely hide all native select styling */
.form-group select::-ms-expand {
    display: none;
}

.form-group select::-webkit-appearance {
    -webkit-appearance: none !important;
}

.form-group select::-moz-appearance {
    -moz-appearance: none !important;
}

.form-group select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

.form-group select:focus + .select-wrapper::after {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23cda565' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

/* Custom Date Picker */
.custom-date-picker {
    position: relative;
    width: 100%;
}

.custom-date-picker input {
    padding-right: 3rem;
    cursor: pointer;
}

.date-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1.2rem;
    height: 1.2rem;
    cursor: pointer;
    z-index: 2;
}

.date-icon svg {
    width: 100%;
    height: 100%;
}

/* Calendar Popup */
.calendar-popup {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(145deg, #ece2d8, rgba(236, 226, 216, 0.95));
    backdrop-filter: blur(25px);
    border: 1px solid rgba(205, 165, 101, 0.25);
    border-radius: 14px;
    box-shadow:
        0 12px 35px rgba(205, 165, 101, 0.12),
        0 5px 15px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    z-index: 1000;
    display: none;
    margin-top: 0.4rem;
    overflow: hidden;
    transform: translateY(-10px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    max-width: 260px;
    margin-left: auto;
    margin-right: auto;
}

.calendar-popup.show {
    display: block;
    transform: translateY(0);
    opacity: 1;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.2rem 0.8rem 1.2rem;
    background: linear-gradient(135deg, rgba(205, 165, 101, 0.05), rgba(205, 165, 101, 0.02));
    border-bottom: 1px solid rgba(205, 165, 101, 0.15);
}

.nav-btn {
    background: linear-gradient(135deg, #cda565, #b8935a);
    color: white;
    border: none;
    width: 2rem;
    height: 2rem;
    border-radius: 10px;
    cursor: pointer;
    font-weight: bold;
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    box-shadow: 0 3px 8px rgba(205, 165, 101, 0.25);
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: linear-gradient(135deg, #b8935a, #a3824f);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(205, 165, 101, 0.4);
}

.close-btn {
    position: absolute;
    top: 0.4rem;
    right: 0.4rem;
    width: 1.2rem;
    height: 1.2rem;
    border: none;
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: rgba(239, 68, 68, 0.2);
    transform: scale(1.1);
}

.nav-btn:active {
    transform: translateY(0) scale(0.98);
}

.month-year {
    font-weight: 600;
    font-size: 0.9rem;
    color: #000;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.2px;
}

.calendar-grid {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.3);
}

.weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.3rem;
    margin-bottom: 0.8rem;
}

.weekdays div {
    text-align: center;
    font-weight: 600;
    color: #cda565;
    padding: 0.4rem 0.2rem;
    font-size: 0.65rem;
    letter-spacing: 0.2px;
    text-transform: uppercase;
    background: rgba(205, 165, 101, 0.08);
    border-radius: 6px;
    border: 1px solid rgba(205, 165, 101, 0.15);
}

.days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.3rem;
}

.day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    font-weight: 500;
    font-size: 0.75rem;
    background: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(205, 165, 101, 0.1);
    color: #000;
    position: relative;
    overflow: hidden;
    min-height: 28px;
}

.day::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(205, 165, 101, 0.1), rgba(205, 165, 101, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.day:hover {
    background: rgba(205, 165, 101, 0.15);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(205, 165, 101, 0.2);
    border-color: rgba(205, 165, 101, 0.3);
}

.day:hover::before {
    opacity: 1;
}

.day.selected {
    background: linear-gradient(135deg, #cda565, #b8935a);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(205, 165, 101, 0.4);
    border-color: #b8935a;
    font-weight: 700;
}

.day.other-month {
    color: #d1d5db;
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(16, 185, 129, 0.05);
}

.day.other-month:hover {
    background: rgba(205, 165, 101, 0.08);
    color: #9ca3af;
}

.day.today {
    background: linear-gradient(135deg, rgba(205, 165, 101, 0.25), rgba(205, 165, 101, 0.15));
    color: #a3824f;
    font-weight: 800;
    border: 2px solid rgba(205, 165, 101, 0.4);
    box-shadow: 0 4px 15px rgba(205, 165, 101, 0.2);
}

.day.today:hover {
    background: linear-gradient(135deg, rgba(205, 165, 101, 0.35), rgba(205, 165, 101, 0.25));
}

/* Hide default date picker icons completely */
.form-group input[type="date"]::-webkit-calendar-picker-indicator {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1.2rem;
    height: 1.2rem;
    opacity: 0 !important;
    cursor: pointer;
    z-index: 10;
    background: transparent !important;
    color: transparent !important;
}

/* Hide other webkit date elements */
.form-group input[type="date"]::-webkit-inner-spin-button,
.form-group input[type="date"]::-webkit-clear-button {
    display: none !important;
    -webkit-appearance: none !important;
}

/* Hide date input text styling */
.form-group input[type="date"]::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
}

.form-group input[type="date"]::-webkit-datetime-edit-month-field,
.form-group input[type="date"]::-webkit-datetime-edit-day-field,
.form-group input[type="date"]::-webkit-datetime-edit-year-field {
    color: #000;
}

/* Style the date input text */
.form-group input[type="date"]::-webkit-datetime-edit {
    color: #000;
}

.form-group input[type="date"]::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
}

.form-group input[type="date"]::-webkit-datetime-edit-text {
    color: #6b7280;
    padding: 0 0.25rem;
}

/* Custom Calendar Styling - Limited browser support */
::-webkit-calendar-picker-indicator {
    filter: invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg) brightness(118%) contrast(119%) !important;
}

/* Try to style the calendar popup - very limited support */
input[type="date"]::-webkit-calendar-picker-indicator:hover {
    background: rgba(16, 185, 129, 0.1) !important;
    border-radius: 4px;
}

/* Alternative approach - try to override system colors */
input[type="date"] {
    color-scheme: light;
}

/* For browsers that support it, try to customize calendar */
@supports (-webkit-appearance: none) {
    input[type="date"]::-webkit-calendar-picker-indicator {
        background: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2310b981' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3e%3c/rect%3e%3cline x1='16' y1='2' x2='16' y2='6'%3e%3c/line%3e%3cline x1='8' y1='2' x2='8' y2='6'%3e%3c/line%3e%3cline x1='3' y1='10' x2='21' y2='10'%3e%3c/line%3e%3c/svg%3e") !important;
        background-size: contain !important;
        opacity: 0 !important;
    }
}

/* Select Options Styling */
.form-group select option {
    background: white;
    color: #000;
    padding: 0.5rem;
    border: none;
}

.form-group select option:checked {
    background: #cda565;
    color: white;
}

/* Better focus states for mobile */
@media (max-width: 768px) {
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        transform: scale(1.02);
        transition: all 0.2s ease;
    }
}

.checkbox-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem;
}

/* Custom Checkbox Styling */
.checkbox-group {
    position: relative;
}

.checkbox-group input[type="checkbox"] {
    width: 0.6rem;
    height: 0.6rem;
    margin-top: 0.05rem;
    margin-right: 0.25rem;
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.checkbox-group input[type="checkbox"]:checked {
    background: #cda565;
    border-color: #cda565;
}

.checkbox-group input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.45rem;
    font-weight: bold;
}

.checkbox-group label {
    margin-bottom: 0;
    font-size: 0.85rem;
    color: #000;
    line-height: 1.4;
}

/* Highlight Dots Around Corp text */
.checkbox-group label,
.trust-description p,
.footer-section h3,
.footer-bottom p {
    color: #6b7280;
}

.checkbox-group label .company-name,
.trust-description .company-name,
.footer-section .company-name,
.footer-bottom .company-name {
    color: #cda565;
    font-weight: 600;
}

/* Form Submit Button */
.form .btn {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #cda565 0%, #b8935a 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
    backdrop-filter: blur(10px);
}

.form .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(205, 165, 101, 0.3);
    background: linear-gradient(135deg, #b8935a 0%, #a3824f 100%);
}

.form .btn:active {
    transform: translateY(0);
}

/* Services Section */
.services {
    padding: 5rem 0;
    background: white;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #000;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-item {
    border-radius: 20px;
    overflow: hidden;
    transition: transform 0.3s ease;
    position: relative;
    box-shadow: none;
    border:1px solid #cda565;
}



.service-item:hover {
    transform: translateY(-10px);
}

.service-icon {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(205, 165, 101, 0.95);
    color: white;
    width: 55px;
    height: 55px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    z-index: 10;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(205, 165, 101, 0.3);
}

.service-image {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-item:hover .service-image img {
    transform: scale(1.1);
}

.service-item h3 {
    padding: 1.5rem 1.5rem 0.5rem;
    color: #000;
    font-size: 1.4rem;
    font-weight: 700;
    position: relative;
    z-index: 2;
}

.service-item p {
    padding: 0 1.5rem 1.5rem;
    color: #000;
    line-height: 1.6;
    position: relative;
    z-index: 2;
    font-weight: 500;
}

/* FAQ Section */
.faq {
    padding: 5rem 0;
    background: #f8f9fa;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: white;
    margin-bottom: 1rem;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s ease;
}

.faq-question:hover {
    background: #f8f9fa;
}

.faq-question h3 {
    color: #000;
    font-size: 1.1rem;
}

.faq-question i {
    color: #cda565;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 1.5rem 1.5rem;
    max-height: 200px;
}

.faq-answer p {
    color: #000;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: #ece2d8;
    color: #000;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #000;
}

.footer-section p {
    color: #000;
    margin-bottom: 0.5rem;
}

.footer-section i {
    margin-right: 0.5rem;
    color: #cda565;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: #000;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #cda565;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(205, 165, 101, 0.3);
    color: #000;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 0 15px;
    }

    .hero-container {
        gap: 2rem;
    }

    .hero-card {
        width: 280px;
    }

    .stat-item {
        width: 180px;
        height: 160px;
    }
}

@media (max-width: 1024px) {
    .hero {
        min-height: 100vh;
        padding: 100px 1.5rem 1rem;
        justify-content: flex-start;
    }

    .hero-container {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
        min-height: auto;
        padding: 2rem 0;
    }

    .hero-content {
        max-width: 100%;
        padding-right: 0;
    }

    .hero-card {
        position: static;
        transform: none;
        margin: 0 auto;
        width: 100%;
        max-width: 320px;
    }

    .hero-title {
        font-size: 2.8rem;
        line-height: 1.1;
        margin-bottom: 2rem;
    }

    .hero-slogan {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .hero-buttons {
        justify-content: center;
        gap: 1.2rem;
    }

    .hero-buttons button {
        min-width: 180px;
        padding: 1rem 1.8rem;
    }

    .hero-stats {
        gap: 1.5rem;
        justify-content: center;
        padding: 2rem 0;
        margin-top: 1rem;
    }

    .stat-item {
        min-width: 160px;
        width: 180px;
        height: 150px;
        padding: 1.5rem 1rem;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .form {
        padding: 2rem;
    }
}

@media (max-width: 768px) {
    /* Navigation */
    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .nav-buttons {
        gap: 0.5rem;
    }

    .btn-touch {
        padding: 0.6rem 1.2rem;
        font-size: 0.85rem;
    }

    .btn-login {
        display: none;
    }

    /* Hero Section */
    .hero {
        min-height: 90vh;
        padding: 80px 1rem 0.5rem;
        justify-content: flex-start;
    }

    .hero-container {
        min-height: auto;
        padding: 1rem 0;
        gap: 2rem;
    }

    .hero-content {
        padding-right: 0;
    }

    .hero-title {
        font-size: 2.1rem;
        line-height: 1.1;
        margin-bottom: 1.5rem;
    }

    .hero-slogan {
        font-size: 0.9rem;
        margin-bottom: 1.2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 0.8rem;
        width: 100%;
        margin-top: 1.2rem;
    }

    .hero-buttons button {
        width: 100%;
        max-width: 280px;
        justify-content: center;
        padding: 1rem 1.3rem;
        font-size: 0.95rem;
    }

    .hero-card {
        display: none;
    }

    .hero-stats {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 1.2rem;
        justify-content: center;
        align-items: center;
        padding: 3rem 0 2rem;
        margin-top: 2rem;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    .stat-item {
        width: calc(50% - 0.6rem);
        min-width: 150px;
        max-width: 180px;
        height: 140px;
        padding: 1.3rem 1rem;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(30px);
        -webkit-backdrop-filter: blur(30px);
        border: 1px solid rgba(255, 255, 255, 0.25);
        box-shadow:
            0 8px 25px rgba(0, 0, 0, 0.25),
            0 4px 12px rgba(16, 185, 129, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }

    .stat-number {
        font-size: 1.8rem;
        margin-bottom: 0.3rem;
        line-height: 1;
    }

    .stat-icon {
        font-size: 1.8rem;
        margin-bottom: 0.8rem;
    }

    .stat-label {
        font-size: 0.7rem;
        line-height: 1.1;
        letter-spacing: 0.3px;
    }

    /* Forms */
    .form-row {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }

    .form {
        padding: 2rem 1.5rem;
        border-radius: 20px;
    }

    .form h3 {
        font-size: 1.6rem;
        margin-bottom: 1.5rem;
    }

    .trust-logos {
        gap: 1.5rem;
        flex-wrap: wrap;
    }

    .form-toggle {
        flex-direction: column;
        align-items: center;
        gap: 0.8rem;
    }

    .toggle-btn {
        width: 100%;
        max-width: 250px;
        padding: 0.8rem 1.5rem;
    }

    /* Services */
    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .section-title {
        font-size: 2.2rem;
        margin-bottom: 2rem;
    }

    /* Trust Section */
    .trust-section {
        padding: 3rem 0;
    }

    .trust-description {
        margin-bottom: 2rem;
    }

    .trust-description p {
        font-size: 1.1rem;
        line-height: 1.7;
    }
}

@media (max-width: 480px) {
    /* Container */
    .container {
        padding: 0 10px;
    }

    /* Header */
    .header {
        padding: 0 1rem;
    }

    .nav {
        padding: 0.8rem 0;
    }

    .logo-text {
        font-size: 1.2rem;
    }

    .logo-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .btn-touch {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .btn-touch span {
        display: none;
    }

    /* Hero Section */
    .hero {
        min-height: 85vh;
        padding: 70px 1rem 0.5rem;
        justify-content: flex-start;
    }

    .hero-container {
        padding: 0.8rem 0;
        gap: 1.5rem;
        min-height: auto;
    }

    .hero-title {
        font-size: 1.7rem;
        line-height: 1.1;
        margin-bottom: 1rem;
    }

    .hero-slogan {
        font-size: 0.8rem;
        margin-bottom: 1rem;
    }

    .hero-buttons {
        margin-top: 1rem;
        gap: 0.7rem;
    }

    .hero-buttons button {
        padding: 0.9rem 1.1rem;
        font-size: 0.85rem;
        max-width: 260px;
        min-height: 44px;
    }

    .hero-card {
        display: none;
    }

    .hero-stats {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 0.8rem;
        justify-content: center;
        padding: 2rem 0 1.2rem;
        margin-top: 1.2rem;
        max-width: 320px;
        margin-left: auto;
        margin-right: auto;
    }

    .stat-item {
        width: calc(50% - 0.4rem);
        min-width: 120px;
        max-width: 140px;
        height: 90px;
        padding: 0.6rem 0.4rem;
        background: rgba(255, 255, 255, 0.18);
        backdrop-filter: blur(35px);
        -webkit-backdrop-filter: blur(35px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow:
            0 6px 20px rgba(0, 0, 0, 0.3),
            0 3px 10px rgba(16, 185, 129, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
        border-radius: 10px;
    }

    .stat-number {
        font-size: 1.2rem;
        margin-bottom: 0.1rem;
        line-height: 1;
        font-weight: 800;
    }

    .stat-icon {
        font-size: 1.3rem;
        margin-bottom: 0.4rem;
    }

    .stat-label {
        font-size: 0.55rem;
        line-height: 1;
        letter-spacing: 0.3px;
        font-weight: 600;
        text-transform: uppercase;
    }

    /* Typography */
    .section-title {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
    }

    /* Trust Section */
    .trust-section {
        padding: 2.5rem 0;
    }

    .trust-description p {
        font-size: 1rem;
        line-height: 1.6;
    }

    .logo-item img {
        height: 60px;
        max-width: 150px;
    }

    /* Forms */
    .form {
        padding: 1.5rem 1rem;
        border-radius: 16px;
        margin: 0 0.5rem;
    }

    .form h3 {
        font-size: 1.4rem;
        margin-bottom: 1.2rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 0.8rem;
        border-radius: 10px;
    }

    .form-group select {
        background-size: 0.8rem;
        padding-right: 2.2rem;
    }

    .form-group input[type="date"] {
        background-size: 0.8rem;
        padding-right: 2.2rem;
    }

    .form-group label {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .toggle-btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.85rem;
    }

    .qouteToggleT {
        font-size: 12px;
    }

    .checkbox-group {
        padding: 0.3rem;
        gap: 0.3rem;
    }

    .checkbox-group label {
        font-size: 0.8rem;
        line-height: 1.3;
    }

    /* Services */
    .service-item h3 {
        font-size: 1.2rem;
        padding: 1.2rem 1.2rem 0.3rem;
    }

    .service-item p {
        padding: 0 1.2rem 1.2rem;
        font-size: 0.9rem;
    }

    .service-icon {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    /* FAQ */
    .faq {
        padding: 3rem 0;
    }

    .faq-question {
        padding: 1.2rem;
    }

    .faq-question h3 {
        font-size: 1rem;
    }

    .faq-item.active .faq-answer {
        padding: 0 1.2rem 1.2rem;
    }

    /* Footer */
    .footer {
        padding: 2rem 0 1rem;
    }

    .footer-content {
        gap: 1.5rem;
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-section h3,
    .footer-section h4 {
        font-size: 1.1rem;
        margin-bottom: 0.8rem;
    }

    .footer-section p {
        font-size: 0.9rem;
    }

    /* Mobile Menu Adjustments */
    .mobile-nav-link {
        font-size: 1.3rem;
        padding: 0.8rem 1.5rem;
    }

    .mobile-btn-touch {
        font-size: 1rem;
        padding: 0.9rem 1.8rem;
    }
}

/* Preloader Animations */
@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes dotPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.3);
        opacity: 1;
    }
}

@keyframes textFade {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes progressFill {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Preloader Responsive Styles */
@media (max-width: 768px) {
    .preloader .logo-container {
        flex-direction: column;
        margin-bottom: 1.5rem;
    }

    .preloader .logo-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .preloader .logo-text {
        font-size: 1.5rem;
    }

    .loading-dots .dot {
        width: 10px;
        height: 10px;
    }

    .loading-text {
        font-size: 1rem;
    }

    .progress-bar {
        width: 150px;
    }
}

@media (max-width: 480px) {
    .preloader .logo-icon {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }

    .preloader .logo-text {
        font-size: 1.3rem;
    }

    .loading-dots .dot {
        width: 8px;
        height: 8px;
    }

    .loading-text {
        font-size: 0.9rem;
    }

    .progress-bar {
        width: 120px;
        height: 3px;
    }
}
