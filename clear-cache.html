<!DOCTYPE html>
<html lang="ka">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache Cleaner - ქეშის გასუფთავება</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ece2d8, #f5f1ec);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #000;
        }

        .container {
            background: rgba(255, 255, 255, 0.9);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        h1 {
            color: #cda565;
            margin-bottom: 1.5rem;
            font-size: 2rem;
        }

        .description {
            margin-bottom: 2rem;
            line-height: 1.6;
            color: #333;
        }

        .btn-group {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .btn {
            background: #cda565;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background: #b8935a;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(205, 165, 101, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: #cda565;
            border: 2px solid #cda565;
        }

        .btn-secondary:hover {
            background: #cda565;
            color: white;
        }

        .instructions {
            background: #ece2d8;
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 2rem;
            text-align: left;
        }

        .instructions h3 {
            color: #cda565;
            margin-bottom: 1rem;
        }

        .instructions ol {
            margin-left: 1.5rem;
        }

        .instructions li {
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .status {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            display: none;
        }

        .status.success {
            background: rgba(205, 165, 101, 0.1);
            color: #b8935a;
            border: 1px solid rgba(205, 165, 101, 0.3);
        }

        .keyboard-shortcuts {
            margin-top: 1rem;
            font-size: 0.9rem;
            color: #666;
        }

        .shortcut {
            background: #f0f0f0;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-family: monospace;
            margin: 0 0.2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 ქეშის გასუფთავება</h1>
        
        <div class="description">
            <p>ეს ფაილი დაგეხმარებათ ბრაუზერის ქეშის გასუფთავებაში, რათა ნახოთ საიტის განახლებული ფერები.</p>
        </div>

        <div class="btn-group">
            <button class="btn" onclick="clearCache()">
                🔄 ქეშის გასუფთავება
            </button>
            
            <button class="btn" onclick="hardRefresh()">
                ⚡ მძლავრი განახლება
            </button>
            
            <a href="index.html" class="btn btn-secondary">
                🏠 მთავარ გვერდზე დაბრუნება
            </a>
        </div>

        <div id="status" class="status"></div>

        <div class="instructions">
            <h3>📋 ინსტრუქციები:</h3>
            <ol>
                <li>დააჭირეთ "ქეშის გასუფთავება" ღილაკს</li>
                <li>ან გამოიყენეთ კლავიატურის კომბინაცია</li>
                <li>დაელოდეთ გვერდის განახლებას</li>
                <li>დაბრუნდით მთავარ გვერდზე</li>
            </ol>
        </div>

        <div class="keyboard-shortcuts">
            <p><strong>კლავიატურის კომბინაციები:</strong></p>
            <p>Windows/Linux: <span class="shortcut">Ctrl + F5</span> ან <span class="shortcut">Ctrl + Shift + R</span></p>
            <p>Mac: <span class="shortcut">Cmd + Shift + R</span></p>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'success') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        function clearCache() {
            // Clear localStorage
            if (typeof(Storage) !== "undefined") {
                localStorage.clear();
                sessionStorage.clear();
            }
            
            // Force reload with cache bypass
            showStatus('ქეში გასუფთავდა! გვერდი განახლდება...', 'success');
            
            setTimeout(() => {
                window.location.reload(true);
            }, 1500);
        }

        function hardRefresh() {
            showStatus('მძლავრი განახლება იწყება...', 'success');
            
            // Clear storage
            if (typeof(Storage) !== "undefined") {
                localStorage.clear();
                sessionStorage.clear();
            }
            
            // Hard refresh
            setTimeout(() => {
                window.location.href = window.location.href + '?t=' + new Date().getTime();
            }, 1000);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+F5 or Ctrl+Shift+R
            if ((e.ctrlKey && e.key === 'F5') || (e.ctrlKey && e.shiftKey && e.key === 'R')) {
                e.preventDefault();
                clearCache();
            }
            
            // Cmd+Shift+R (Mac)
            if (e.metaKey && e.shiftKey && e.key === 'R') {
                e.preventDefault();
                clearCache();
            }
        });

        // Auto-clear cache on page load
        window.addEventListener('load', function() {
            // Add timestamp to prevent caching of this page itself
            if (!window.location.search.includes('nocache')) {
                const url = new URL(window.location);
                url.searchParams.set('nocache', Date.now());
                window.history.replaceState({}, '', url);
            }
        });
    </script>
</body>
</html>
