# Dots Around Corp - Auto Transport Landing Page

A modern, responsive landing page for Dots Around Corp's auto transport services, featuring smooth GSAP animations and interactive forms.

## Features

### 🎨 Design & Animation
- **Responsive Design**: Fully responsive layout that works on all devices
- **GSAP Animations**: Smooth entrance animations, scroll-triggered effects, and interactive transitions
- **Modern UI**: Clean, professional design with hover effects and smooth transitions
- **Sticky Header**: Head<PERSON> shrinks and changes appearance on scroll

### 📝 Interactive Forms
- **Dual Form System**: Toggle between "Get Quote" and "Contact Us" forms
- **Form Validation**: Client-side validation with user-friendly error messages
- **Smooth Transitions**: Animated form switching with GSAP
- **PHP Backend Support**: Optional PHP form handler included

### 🚀 Sections

1. **Hero Section**
   - Full-width background image
   - Animated title and call-to-action buttons
   - Smooth scroll to forms

2. **Trust Section**
   - Company logos (FMCSA, DOT, CentralDispatch)
   - Company description
   - Interactive forms with toggle functionality

3. **Services Section**
   - 5 service offerings with images and descriptions
   - Scroll-triggered animations
   - Hover effects on service cards

4. **FAQ Section**
   - Accordion-style expandable questions
   - Smooth GSAP animations for expand/collapse
   - 6 common questions about auto transport

5. **Footer**
   - Company information
   - Contact details
   - Social media links

## File Structure

```
dotsaround/
├── index.html          # Main HTML file
├── styles.css          # CSS styles and responsive design
├── script.js           # JavaScript with GSAP animations
├── form-handler.php    # Optional PHP backend for forms
└── README.md          # This file
```

## Setup Instructions

### Basic Setup (HTML/CSS/JS only)
1. Open `index.html` in a web browser
2. The page will work with simulated form submissions
3. All animations and interactions are functional

### Advanced Setup (with PHP backend)
1. **Requirements**: PHP 7.0+ and a web server (Apache/Nginx)
2. **Upload files** to your web server
3. **Enable PHP form handling**:
   - Edit `script.js`
   - Uncomment the `fetch()` blocks in `submitQuoteForm()` and `submitContactForm()`
   - Comment out the `setTimeout()` simulation blocks
4. **Configure email settings** in `form-handler.php`:
   - Update email addresses in the email functions
   - Uncomment `mail()` function calls to enable email notifications
5. **Set permissions**: Ensure the web server can create `quotes/` and `contacts/` directories

## Customization

### Images
Replace placeholder images with actual photos:
- **Hero background**: Update the background image URL in `styles.css` (line ~75)
- **Service images**: Replace placeholder URLs in `index.html` service section
- **Company logos**: Replace placeholder logo URLs with actual FMCSA, DOT, and CentralDispatch logos

### Content
- **Company information**: Update contact details in the footer
- **Services**: Modify service descriptions and add/remove services as needed
- **FAQ**: Update questions and answers to match your business needs

### Styling
- **Colors**: Update the color scheme in `styles.css` by modifying CSS custom properties
- **Fonts**: Change the Google Fonts import in `index.html` and update font-family in CSS
- **Layout**: Adjust spacing, sizing, and layout in the CSS file

### Animations
- **Timing**: Adjust animation durations and delays in `script.js`
- **Effects**: Modify GSAP animation properties for different entrance effects
- **Triggers**: Change ScrollTrigger start/end points for different animation timing

## Browser Support

- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile**: iOS Safari, Chrome Mobile, Samsung Internet
- **GSAP**: Requires modern browser with ES6 support

## Dependencies

### External Libraries (CDN)
- **GSAP 3.12.2**: Animation library
- **ScrollTrigger**: GSAP plugin for scroll-based animations
- **Font Awesome 6.0**: Icons
- **Google Fonts**: Inter font family

### No Build Process Required
All dependencies are loaded via CDN, so no build tools or package managers are needed.

## Performance Optimization

- **Images**: Optimize images for web (WebP format recommended)
- **CDN**: All external libraries loaded from CDN for fast loading
- **Lazy Loading**: Consider adding lazy loading for images below the fold
- **Minification**: Minify CSS and JS files for production

## Form Data Handling

### Client-Side (Default)
- Forms show success/error notifications
- Data is validated but not stored
- Perfect for testing and development

### Server-Side (PHP)
- Form data saved to JSON files in `quotes/` and `contacts/` directories
- Email notifications sent to admin and customer
- Unique IDs generated for each submission
- Input sanitization and validation

## Security Considerations

When using the PHP backend:
- **Input Validation**: All inputs are sanitized and validated
- **CSRF Protection**: Consider adding CSRF tokens for production
- **Rate Limiting**: Implement rate limiting to prevent spam
- **SSL**: Use HTTPS in production
- **File Permissions**: Secure file permissions on the server

## Troubleshooting

### Common Issues
1. **Animations not working**: Check browser console for GSAP loading errors
2. **Forms not submitting**: Verify PHP configuration and file permissions
3. **Mobile layout issues**: Test responsive breakpoints in browser dev tools
4. **Images not loading**: Check image URLs and file paths

### Debug Mode
Add `?debug=1` to the URL to enable console logging for animations and form submissions.

## License

This project is created for Dots Around Corp. Modify and use according to your needs.

## Support

For technical support or customization requests, please contact the development team.
