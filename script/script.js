// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// Preloader functionality
window.addEventListener('load', function() {
    const preloader = document.getElementById('preloader');

    // Simulate loading time (minimum 2 seconds for better UX)
    setTimeout(() => {
        preloader.classList.add('fade-out');

        // Remove preloader from DOM after fade out
        setTimeout(() => {
            preloader.remove();
        }, 500);
    }, 2000);
});

// DOM Elements
const header = document.getElementById('header');
const headerGetInTouch = document.getElementById('headerGetInTouch');
const getInTouchBtn = document.getElementById('getInTouchBtn');
const ourServicesBtn = document.getElementById('ourServicesBtn');
const quoteToggle = document.getElementById('quoteToggle');
const contactToggle = document.getElementById('contactToggle');
const quoteForm = document.getElementById('quoteForm');
const contactForm = document.getElementById('contactForm');
const faqItems = document.querySelectorAll('.faq-item');
const mobileMenuToggle = document.getElementById('mobileMenuToggle');
const mobileMenu = document.getElementById('mobileMenu');
const mobileGetInTouch = document.getElementById('mobileGetInTouch');

// Initialize animations when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Ensure buttons are visible
    const heroButtons = document.querySelectorAll('.hero-buttons .btn');
    heroButtons.forEach(btn => {
        btn.style.display = 'flex';
        btn.style.visibility = 'visible';
        btn.style.opacity = '1';
    });

    initAnimations();
    initFormHandling();
    initFAQ();
    initSmoothScroll();
    initHeaderScroll();
    initHeroCardDots();
    initMobileMenu();
});

// Initialize all GSAP animations
function initAnimations() {
    // Hero section animations
    const heroTl = gsap.timeline({ delay: 0.5 });

    // Animate hero content
    heroTl.from('.hero-slogan', {
        duration: 0.8,
        y: 30,
        opacity: 0,
        ease: 'power2.out'
    })
    .from('.hero-title', {
        duration: 1.2,
        y: 50,
        opacity: 0,
        ease: 'power3.out'
    }, '-=0.4')
    // .from('.hero-buttons .btn', {
    //     duration: 0.8,
    //     y: 30,
    //     opacity: 0,
    //     stagger: 0.2,
    //     ease: 'power2.out',
    //     clearProps: "all"
    // }, '-=0.6')
    .from('.hero-card', {
        duration: 1,
        x: 100,
        opacity: 0,
        ease: 'power2.out'
    }, '-=0.8')
    .from('.stat-item', {
        duration: 0.8,
        y: 50,
        opacity: 0,
        stagger: 0.2,
        ease: 'power2.out',
        clearProps: "all"
    }, '-=0.4');

    // Hero card hover animation
    const heroCard = document.querySelector('.hero-card');
    if (heroCard) {
        heroCard.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.3,
                y: -10,
                scale: 1.02,
                ease: 'power2.out'
            });
        });

        heroCard.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.3,
                y: 0,
                scale: 1,
                ease: 'power2.out'
            });
        });
    }

    // Stats animation is handled in initAnimations() function above

    // Trust section animations
    gsap.from('.trust-logos .logo-item', {
        scrollTrigger: {
            trigger: '.trust-section',
            start: 'top 80%'
        },
        duration: 0.8,
        y: 50,
        opacity: 0,
        stagger: 0.2,
        ease: 'power2.out'
    });

    gsap.from('.trust-description', {
        scrollTrigger: {
            trigger: '.trust-description',
            start: 'top 80%'
        },
        duration: 1,
        y: 30,
        opacity: 0,
        ease: 'power2.out'
    });

    gsap.from('.form-toggle', {
        scrollTrigger: {
            trigger: '.form-toggle',
            start: 'top 80%'
        },
        duration: 0.8,
        y: 30,
        opacity: 0,
        ease: 'power2.out'
    });

    // Services section animations
    gsap.from('.section-title', {
        scrollTrigger: {
            trigger: '.services',
            start: 'top 80%'
        },
        duration: 1,
        y: 50,
        opacity: 0,
        ease: 'power2.out'
    });

    gsap.from('.service-item', {
        scrollTrigger: {
            trigger: '.services-grid',
            start: 'top 80%'
        },
        duration: 0.8,
        y: 80,
        opacity: 0,
        stagger: 0.2,
        ease: 'power2.out'
    });

    // FAQ section animations
    gsap.from('.faq .section-title', {
        scrollTrigger: {
            trigger: '.faq',
            start: 'top 80%'
        },
        duration: 1,
        y: 50,
        opacity: 0,
        ease: 'power2.out'
    });

    gsap.from('.faq-item', {
        scrollTrigger: {
            trigger: '.faq-container',
            start: 'top 80%'
        },
        duration: 0.6,
        y: 30,
        opacity: 0,
        stagger: 0.1,
        ease: 'power2.out'
    });

    // Footer animation
    gsap.from('.footer-content', {
        scrollTrigger: {
            trigger: '.footer',
            start: 'top 90%'
        },
        duration: 1,
        y: 50,
        opacity: 0,
        ease: 'power2.out'
    });

    // Button hover animations
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.3,
                scale: 1.05,
                ease: 'power2.out'
            });
        });

        btn.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.3,
                scale: 1,
                ease: 'power2.out'
            });
        });
    });
}

// Form handling and toggle functionality
function initFormHandling() {
    // Form toggle functionality
    quoteToggle.addEventListener('click', function() {
        switchForm('quote');
    });

    contactToggle.addEventListener('click', function() {
        switchForm('contact');
    });

    // Hero button functionality
    headerGetInTouch.addEventListener('click', function() {
        switchForm('contact');
        scrollToElement('.trust-section');
    });

    getInTouchBtn.addEventListener('click', function() {
        switchForm('contact');
        scrollToElement('.trust-section');
    });

    ourServicesBtn.addEventListener('click', function() {
        scrollToElement('.services');
    });

    // Form submissions
    quoteForm.addEventListener('submit', function(e) {
        e.preventDefault();
        if (validateQuoteForm()) {
            submitQuoteForm();
        }
    });

    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        if (validateContactForm()) {
            submitContactForm();
        }
    });
}

// Switch between forms with animation
function switchForm(formType) {
    const isQuote = formType === 'quote';
    
    // Update toggle buttons
    quoteToggle.classList.toggle('active', isQuote);
    contactToggle.classList.toggle('active', !isQuote);
    
    // Animate form transition
    const currentForm = document.querySelector('.form.active');
    const targetForm = isQuote ? quoteForm : contactForm;
    
    if (currentForm !== targetForm) {
        // Animate out current form
        gsap.to(currentForm, {
            duration: 0.3,
            opacity: 0,
            y: -20,
            ease: 'power2.inOut',
            onComplete: function() {
                currentForm.classList.remove('active');
                targetForm.classList.add('active');
                
                // Animate in new form
                gsap.fromTo(targetForm, 
                    { opacity: 0, y: 20 },
                    { 
                        duration: 0.3,
                        opacity: 1,
                        y: 0,
                        ease: 'power2.out'
                    }
                );
            }
        });
    }
}

// Form validation
function validateQuoteForm() {
    const zipFrom = document.getElementById('zipFrom').value;
    const zipTo = document.getElementById('zipTo').value;
    const vehicleType = document.getElementById('vehicleType').value;
    const shipDate = document.getElementById('shipDate').value;
    const email = document.getElementById('email').value;
    const phone = document.getElementById('phone').value;
    const agreement = document.getElementById('agreement').checked;

    if (!zipFrom || !zipTo || !vehicleType || !shipDate || !email || !phone || !agreement) {
        showNotification('Please fill in all required fields and accept the agreement.', 'error');
        return false;
    }

    if (!isValidEmail(email)) {
        showNotification('Please enter a valid email address.', 'error');
        return false;
    }

    return true;
}

function validateContactForm() {
    const whoAreYou = document.getElementById('whoAreYou').value;
    const contactName = document.getElementById('contactName').value;
    const contactEmail = document.getElementById('contactEmail').value;
    const message = document.getElementById('message').value;

    if (!whoAreYou || !contactName || !contactEmail || !message) {
        showNotification('Please fill in all required fields.', 'error');
        return false;
    }

    if (!isValidEmail(contactEmail)) {
        showNotification('Please enter a valid email address.', 'error');
        return false;
    }

    return true;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Form submission handlers
function submitQuoteForm() {
    // Show loading state
    const submitBtn = quoteForm.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Sending...';
    submitBtn.disabled = true;

    // Collect form data
    const formData = {
        formType: 'quote',
        zipFrom: document.getElementById('zipFrom').value,
        zipTo: document.getElementById('zipTo').value,
        vehicleType: document.getElementById('vehicleType').value,
        shipDate: document.getElementById('shipDate').value,
        email: document.getElementById('email').value,
        phone: document.getElementById('phone').value,
        agreement: document.getElementById('agreement').checked
    };

    // Submit to PHP handler (uncomment to use PHP backend)
    /*
    fetch('form-handler.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            quoteForm.reset();
        } else {
            showNotification(data.error || 'An error occurred', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Network error. Please try again.', 'error');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
    */

    // Simulate form submission for demo (remove when using PHP)
    setTimeout(() => {
        showNotification('Quote request submitted successfully! We\'ll contact you soon.', 'success');
        quoteForm.reset();
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

function submitContactForm() {
    // Show loading state
    const submitBtn = contactForm.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Sending...';
    submitBtn.disabled = true;

    // Collect form data
    const formData = {
        formType: 'contact',
        whoAreYou: document.getElementById('whoAreYou').value,
        contactName: document.getElementById('contactName').value,
        contactEmail: document.getElementById('contactEmail').value,
        message: document.getElementById('message').value
    };

    // Submit to PHP handler (uncomment to use PHP backend)
    /*
    fetch('form-handler.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            contactForm.reset();
        } else {
            showNotification(data.error || 'An error occurred', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Network error. Please try again.', 'error');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
    */

    // Simulate form submission for demo (remove when using PHP)
    setTimeout(() => {
        showNotification('Message sent successfully! We\'ll get back to you soon.', 'success');
        contactForm.reset();
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

// Notification system
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add styles
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '1rem 1.5rem',
        borderRadius: '8px',
        color: 'white',
        fontWeight: '500',
        zIndex: '10000',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        backgroundColor: type === 'success' ? '#27ae60' : '#e74c3c'
    });

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 5000);
}

// FAQ functionality
function initFAQ() {
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        
        question.addEventListener('click', function() {
            const isActive = item.classList.contains('active');
            
            // Close all other FAQ items
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                    gsap.to(otherItem.querySelector('.faq-answer'), {
                        duration: 0.3,
                        height: 0,
                        paddingTop: 0,
                        paddingBottom: 0,
                        ease: 'power2.inOut'
                    });
                }
            });
            
            // Toggle current item
            if (isActive) {
                item.classList.remove('active');
                gsap.to(answer, {
                    duration: 0.3,
                    height: 0,
                    paddingTop: 0,
                    paddingBottom: 0,
                    ease: 'power2.inOut'
                });
            } else {
                item.classList.add('active');
                gsap.set(answer, { height: 'auto' });
                const height = answer.offsetHeight;
                gsap.fromTo(answer, 
                    { height: 0, paddingTop: 0, paddingBottom: 0 },
                    { 
                        duration: 0.3,
                        height: height,
                        paddingTop: '0px',
                        paddingBottom: '1.5rem',
                        ease: 'power2.out'
                    }
                );
            }
        });
    });
}

// Smooth scroll functionality
function initSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                scrollToElement(target);
            }
        });
    });
}

function scrollToElement(element) {
    const targetElement = typeof element === 'string' ? document.querySelector(element) : element;
    if (targetElement) {
        const targetPosition = targetElement.offsetTop - 80;
        window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
        });
    }
}

// Header scroll effect
function initHeaderScroll() {
    ScrollTrigger.create({
        trigger: 'body',
        start: 'top -50px',
        end: 'bottom bottom',
        onEnter: () => header.classList.add('scrolled'),
        onLeaveBack: () => header.classList.remove('scrolled')
    });
}

// Hero card dots functionality
function initHeroCardDots() {
    const dots = document.querySelectorAll('.dot');
    const cardImage = document.querySelector('.card-image img');

    const images = [
        'https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
        'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
    ];

    if (dots.length > 0 && cardImage) {
        dots.forEach((dot, index) => {
            dot.addEventListener('click', function() {
                // Remove active class from all dots
                dots.forEach(d => d.classList.remove('active'));

                // Add active class to clicked dot
                this.classList.add('active');

                // Animate image change
                gsap.to(cardImage, {
                    duration: 0.3,
                    opacity: 0,
                    ease: 'power2.inOut',
                    onComplete: function() {
                        cardImage.src = images[index];
                        gsap.to(cardImage, {
                            duration: 0.3,
                            opacity: 1,
                            ease: 'power2.out'
                        });
                    }
                });
            });
        });

        // Auto-rotate dots every 4 seconds
        let currentDot = 0;
        setInterval(() => {
            currentDot = (currentDot + 1) % dots.length;
            dots[currentDot].click();
        }, 4000);
    }
}

// Custom Date Picker
class CustomDatePicker {
    constructor(inputId) {
        this.input = document.getElementById(inputId);
        this.popup = document.getElementById('calendarPopup');
        this.monthYear = document.getElementById('monthYear');
        this.daysContainer = document.getElementById('calendarDays');
        this.prevBtn = document.getElementById('prevMonth');
        this.nextBtn = document.getElementById('nextMonth');
        this.closeBtn = document.getElementById('closeCalendar');

        this.currentDate = new Date();
        this.selectedDate = null;

        this.init();
    }

    init() {
        this.input.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleCalendar();
        });

        this.input.parentElement.querySelector('.date-icon').addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleCalendar();
        });

        this.prevBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.previousMonth();
        });

        this.nextBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.nextMonth();
        });

        this.closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.hideCalendar();
        });

        // Prevent calendar from closing when clicking inside it
        this.popup.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Temporarily disable outside click - only close with ESC or date selection
        // this.outsideClickHandler = (e) => {
        //     // Check if calendar is visible first
        //     if (!this.popup.classList.contains('show')) {
        //         return;
        //     }

        //     // Check if click is outside both input container and popup
        //     const inputContainer = this.input.parentElement;
        //     const isClickInsideInput = inputContainer.contains(e.target);
        //     const isClickInsidePopup = this.popup.contains(e.target);

        //     if (!isClickInsideInput && !isClickInsidePopup) {
        //         this.hideCalendar();
        //     }
        // };
        // document.addEventListener('click', this.outsideClickHandler);

        // Close calendar on Escape key
        this.escapeKeyHandler = (e) => {
            if (e.key === 'Escape') {
                this.hideCalendar();
            }
        };
        document.addEventListener('keydown', this.escapeKeyHandler);

        this.renderCalendar();
    }

    toggleCalendar() {
        if (this.popup.classList.contains('show')) {
            this.hideCalendar();
        } else {
            this.showCalendar();
        }
    }

    showCalendar() {
        this.popup.classList.add('show');
        this.renderCalendar();

        // Add smooth animation
        setTimeout(() => {
            this.popup.style.transform = 'translateY(0)';
            this.popup.style.opacity = '1';
        }, 10);
    }

    hideCalendar() {
        this.popup.style.transform = 'translateY(-10px)';
        this.popup.style.opacity = '0';

        setTimeout(() => {
            this.popup.classList.remove('show');
        }, 300);
    }

    destroy() {
        // if (this.outsideClickHandler) {
        //     document.removeEventListener('click', this.outsideClickHandler);
        // }
        if (this.escapeKeyHandler) {
            document.removeEventListener('keydown', this.escapeKeyHandler);
        }
    }

    previousMonth() {
        this.currentDate.setMonth(this.currentDate.getMonth() - 1);
        this.renderCalendar();
    }

    nextMonth() {
        this.currentDate.setMonth(this.currentDate.getMonth() + 1);
        this.renderCalendar();
    }

    renderCalendar() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();

        // Update month/year display
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                           'July', 'August', 'September', 'October', 'November', 'December'];
        this.monthYear.textContent = `${monthNames[month]} ${year}`;

        // Clear days container
        this.daysContainer.innerHTML = '';

        // Get first day of month and number of days
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = (firstDay.getDay() + 6) % 7; // Monday = 0

        // Add empty cells for previous month
        for (let i = 0; i < startingDayOfWeek; i++) {
            const prevDate = new Date(year, month, -startingDayOfWeek + i + 1);
            const dayElement = this.createDayElement(prevDate.getDate(), true);
            this.daysContainer.appendChild(dayElement);
        }

        // Add days of current month
        for (let day = 1; day <= daysInMonth; day++) {
            const dayElement = this.createDayElement(day, false);
            this.daysContainer.appendChild(dayElement);
        }

        // Add days from next month to fill the grid
        const totalCells = this.daysContainer.children.length;
        const remainingCells = 42 - totalCells; // 6 rows × 7 days
        for (let day = 1; day <= remainingCells; day++) {
            const dayElement = this.createDayElement(day, true);
            this.daysContainer.appendChild(dayElement);
        }
    }

    createDayElement(day, isOtherMonth) {
        const dayElement = document.createElement('div');
        dayElement.className = 'day';
        dayElement.textContent = day;

        if (isOtherMonth) {
            dayElement.classList.add('other-month');
        }

        // Check if it's today
        const today = new Date();
        const currentMonth = this.currentDate.getMonth();
        const currentYear = this.currentDate.getFullYear();

        if (!isOtherMonth &&
            day === today.getDate() &&
            currentMonth === today.getMonth() &&
            currentYear === today.getFullYear()) {
            dayElement.classList.add('today');
        }

        // Check if it's selected
        if (this.selectedDate &&
            !isOtherMonth &&
            day === this.selectedDate.getDate() &&
            currentMonth === this.selectedDate.getMonth() &&
            currentYear === this.selectedDate.getFullYear()) {
            dayElement.classList.add('selected');
        }

        dayElement.addEventListener('click', (e) => {
            e.stopPropagation();
            if (!isOtherMonth) {
                this.selectDate(new Date(currentYear, currentMonth, day));
            }
        });

        return dayElement;
    }

    selectDate(date) {
        this.selectedDate = date;

        // Format date nicely for display
        const options = {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        };
        const displayDate = date.toLocaleDateString('en-US', options);
        this.input.value = displayDate;

        // Store the actual date value for form submission
        this.input.setAttribute('data-value', date.toLocaleDateString('en-CA'));

        this.hideCalendar();
        this.renderCalendar();
    }
}

// Mobile Menu functionality
function initMobileMenu() {
    if (!mobileMenuToggle || !mobileMenu) return;

    mobileMenuToggle.addEventListener('click', function() {
        toggleMobileMenu();
    });

    // Mobile Get in Touch button
    if (mobileGetInTouch) {
        mobileGetInTouch.addEventListener('click', function() {
            closeMobileMenu();
            switchForm('contact');
            scrollToElement('.trust-section');
        });
    }

    // Close menu when clicking on nav links
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
    mobileNavLinks.forEach(link => {
        link.addEventListener('click', function() {
            closeMobileMenu();
        });
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (mobileMenu.classList.contains('active') &&
            !mobileMenu.contains(e.target) &&
            !mobileMenuToggle.contains(e.target)) {
            closeMobileMenu();
        }
    });

    // Close menu on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && mobileMenu.classList.contains('active')) {
            closeMobileMenu();
        }
    });
}

function toggleMobileMenu() {
    const isActive = mobileMenu.classList.contains('active');

    if (isActive) {
        closeMobileMenu();
    } else {
        openMobileMenu();
    }
}

function openMobileMenu() {
    mobileMenu.classList.add('active');
    mobileMenuToggle.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Animate menu items
    const menuItems = document.querySelectorAll('.mobile-nav-link, .mobile-btn-touch');
    gsap.fromTo(menuItems,
        { opacity: 0, y: 30 },
        {
            opacity: 1,
            y: 0,
            duration: 0.3,
            stagger: 0.1,
            delay: 0.2,
            ease: 'power2.out'
        }
    );
}

function closeMobileMenu() {
    mobileMenu.classList.remove('active');
    mobileMenuToggle.classList.remove('active');
    document.body.style.overflow = '';
}

// Initialize custom date picker
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for the DOM to be fully ready
    setTimeout(() => {
        if (document.getElementById('shipDate')) {
            new CustomDatePicker('shipDate');
        }
    }, 100);
});
