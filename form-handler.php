<?php
// Enable CORS for development (remove in production)
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!$input) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON input']);
    exit();
}

// Determine form type
$formType = $input['formType'] ?? '';

if ($formType === 'quote') {
    handleQuoteForm($input);
} elseif ($formType === 'contact') {
    handleContactForm($input);
} else {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid form type']);
}

function handleQuoteForm($data) {
    // Validate required fields
    $requiredFields = ['zipFrom', 'zipTo', 'vehicleType', 'shipDate', 'email', 'phone', 'agreement'];
    
    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Missing required field: $field"]);
            return;
        }
    }
    
    // Validate email
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid email address']);
        return;
    }
    
    // Validate agreement checkbox
    if ($data['agreement'] !== true && $data['agreement'] !== 'true') {
        http_response_code(400);
        echo json_encode(['error' => 'Agreement must be accepted']);
        return;
    }
    
    // Sanitize data
    $sanitizedData = [
        'zipFrom' => sanitizeInput($data['zipFrom']),
        'zipTo' => sanitizeInput($data['zipTo']),
        'vehicleType' => sanitizeInput($data['vehicleType']),
        'shipDate' => sanitizeInput($data['shipDate']),
        'email' => filter_var($data['email'], FILTER_SANITIZE_EMAIL),
        'phone' => sanitizeInput($data['phone']),
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    // Process quote request
    $result = processQuoteRequest($sanitizedData);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'message' => 'Quote request submitted successfully! We\'ll contact you soon.',
            'quoteId' => $result['quoteId']
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to process quote request']);
    }
}

function handleContactForm($data) {
    // Validate required fields
    $requiredFields = ['whoAreYou', 'contactName', 'contactEmail', 'message'];
    
    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            http_response_code(400);
            echo json_encode(['error' => "Missing required field: $field"]);
            return;
        }
    }
    
    // Validate email
    if (!filter_var($data['contactEmail'], FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid email address']);
        return;
    }
    
    // Sanitize data
    $sanitizedData = [
        'whoAreYou' => sanitizeInput($data['whoAreYou']),
        'contactName' => sanitizeInput($data['contactName']),
        'contactEmail' => filter_var($data['contactEmail'], FILTER_SANITIZE_EMAIL),
        'message' => sanitizeInput($data['message']),
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    // Process contact request
    $result = processContactRequest($sanitizedData);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'message' => 'Message sent successfully! We\'ll get back to you soon.',
            'contactId' => $result['contactId']
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to process contact request']);
    }
}

function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function processQuoteRequest($data) {
    // Generate unique quote ID
    $quoteId = 'QT' . date('Ymd') . rand(1000, 9999);
    
    // Save to database (implement your database logic here)
    // For now, we'll save to a JSON file for demonstration
    $success = saveQuoteToFile($data, $quoteId);
    
    // Send email notification (implement your email logic here)
    if ($success) {
        sendQuoteNotificationEmail($data, $quoteId);
    }
    
    return [
        'success' => $success,
        'quoteId' => $quoteId
    ];
}

function processContactRequest($data) {
    // Generate unique contact ID
    $contactId = 'CT' . date('Ymd') . rand(1000, 9999);
    
    // Save to database (implement your database logic here)
    // For now, we'll save to a JSON file for demonstration
    $success = saveContactToFile($data, $contactId);
    
    // Send email notification (implement your email logic here)
    if ($success) {
        sendContactNotificationEmail($data, $contactId);
    }
    
    return [
        'success' => $success,
        'contactId' => $contactId
    ];
}

function saveQuoteToFile($data, $quoteId) {
    $data['quoteId'] = $quoteId;
    $filename = 'quotes/' . $quoteId . '.json';
    
    // Create quotes directory if it doesn't exist
    if (!is_dir('quotes')) {
        mkdir('quotes', 0755, true);
    }
    
    return file_put_contents($filename, json_encode($data, JSON_PRETTY_PRINT)) !== false;
}

function saveContactToFile($data, $contactId) {
    $data['contactId'] = $contactId;
    $filename = 'contacts/' . $contactId . '.json';
    
    // Create contacts directory if it doesn't exist
    if (!is_dir('contacts')) {
        mkdir('contacts', 0755, true);
    }
    
    return file_put_contents($filename, json_encode($data, JSON_PRETTY_PRINT)) !== false;
}

function sendQuoteNotificationEmail($data, $quoteId) {
    // Email configuration
    $to = '<EMAIL>'; // Replace with your email
    $subject = 'New Quote Request - ' . $quoteId;
    
    $message = "New quote request received:\n\n";
    $message .= "Quote ID: " . $quoteId . "\n";
    $message .= "From Zip: " . $data['zipFrom'] . "\n";
    $message .= "To Zip: " . $data['zipTo'] . "\n";
    $message .= "Vehicle Type: " . $data['vehicleType'] . "\n";
    $message .= "Ship Date: " . $data['shipDate'] . "\n";
    $message .= "Email: " . $data['email'] . "\n";
    $message .= "Phone: " . $data['phone'] . "\n";
    $message .= "Timestamp: " . $data['timestamp'] . "\n";
    
    $headers = "From: <EMAIL>\r\n";
    $headers .= "Reply-To: " . $data['email'] . "\r\n";
    $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
    
    // Send email (uncomment to enable)
    // mail($to, $subject, $message, $headers);
    
    // Also send confirmation email to customer
    $customerSubject = 'Quote Request Confirmation - Dots Around Corp';
    $customerMessage = "Dear Customer,\n\n";
    $customerMessage .= "Thank you for your quote request. We have received your information and will contact you soon.\n\n";
    $customerMessage .= "Quote ID: " . $quoteId . "\n";
    $customerMessage .= "Route: " . $data['zipFrom'] . " to " . $data['zipTo'] . "\n";
    $customerMessage .= "Vehicle: " . $data['vehicleType'] . "\n\n";
    $customerMessage .= "Best regards,\nDots Around Corp Team";
    
    $customerHeaders = "From: <EMAIL>\r\n";
    $customerHeaders .= "Content-Type: text/plain; charset=UTF-8\r\n";
    
    // Send confirmation email (uncomment to enable)
    // mail($data['email'], $customerSubject, $customerMessage, $customerHeaders);
}

function sendContactNotificationEmail($data, $contactId) {
    // Email configuration
    $to = '<EMAIL>'; // Replace with your email
    $subject = 'New Contact Message - ' . $contactId;
    
    $message = "New contact message received:\n\n";
    $message .= "Contact ID: " . $contactId . "\n";
    $message .= "Who Are You: " . $data['whoAreYou'] . "\n";
    $message .= "Name: " . $data['contactName'] . "\n";
    $message .= "Email: " . $data['contactEmail'] . "\n";
    $message .= "Message: " . $data['message'] . "\n";
    $message .= "Timestamp: " . $data['timestamp'] . "\n";
    
    $headers = "From: <EMAIL>\r\n";
    $headers .= "Reply-To: " . $data['contactEmail'] . "\r\n";
    $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
    
    // Send email (uncomment to enable)
    // mail($to, $subject, $message, $headers);
    
    // Also send confirmation email to customer
    $customerSubject = 'Message Received - Dots Around Corp';
    $customerMessage = "Dear " . $data['contactName'] . ",\n\n";
    $customerMessage .= "Thank you for contacting us. We have received your message and will get back to you soon.\n\n";
    $customerMessage .= "Contact ID: " . $contactId . "\n\n";
    $customerMessage .= "Best regards,\nDots Around Corp Team";
    
    $customerHeaders = "From: <EMAIL>\r\n";
    $customerHeaders .= "Content-Type: text/plain; charset=UTF-8\r\n";
    
    // Send confirmation email (uncomment to enable)
    // mail($data['contactEmail'], $customerSubject, $customerMessage, $customerHeaders);
}
?>
